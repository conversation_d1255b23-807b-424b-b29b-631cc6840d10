const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');

// Mock web contents
const mockWebContents = {
  executeJavaScript: jest.fn(),
  isDestroyed: jest.fn(() => false)
};

// Import the class to test
const YouTubeAdSkipper = require('../src/youtube-ad-skipper');

describe('YouTubeAdSkipper', () => {
  let adSkipper;

  beforeEach(() => {
    adSkipper = new YouTubeAdSkipper();
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    if (adSkipper) {
      adSkipper.stopAdDetection();
    }
    jest.useRealTimers();
  });

  describe('initialization', () => {
    test('should initialize with default values', () => {
      expect(adSkipper.isEnabled).toBe(true);
      expect(adSkipper.adSkippedCount).toBe(0);
      expect(adSkipper.webContents).toBe(null);
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should have correct detection interval', () => {
      expect(adSkipper.adDetectionInterval).toBe(1000);
    });
  });

  describe('registerWebContents', () => {
    test('should register web contents and start detection', () => {
      const startDetectionSpy = jest.spyOn(adSkipper, 'startAdDetection');
      
      adSkipper.registerWebContents(mockWebContents);
      
      expect(adSkipper.webContents).toBe(mockWebContents);
      expect(startDetectionSpy).toHaveBeenCalled();
    });

    test('should log registration message', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      adSkipper.registerWebContents(mockWebContents);
      
      expect(consoleSpy).toHaveBeenCalledWith('YouTube Ad Skipper: Web contents registered');
      consoleSpy.mockRestore();
    });
  });

  describe('unregisterWebContents', () => {
    test('should unregister web contents and stop detection', () => {
      adSkipper.registerWebContents(mockWebContents);
      const stopDetectionSpy = jest.spyOn(adSkipper, 'stopAdDetection');
      
      adSkipper.unregisterWebContents();
      
      expect(adSkipper.webContents).toBe(null);
      expect(stopDetectionSpy).toHaveBeenCalled();
    });
  });

  describe('startAdDetection', () => {
    test('should start interval when enabled and web contents registered', () => {
      adSkipper.registerWebContents(mockWebContents);
      
      expect(adSkipper.checkInterval).not.toBe(null);
    });

    test('should not start interval when disabled', () => {
      adSkipper.isEnabled = false;
      adSkipper.registerWebContents(mockWebContents);
      
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should not start interval when no web contents', () => {
      adSkipper.startAdDetection();
      
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should clear existing interval before starting new one', () => {
      adSkipper.registerWebContents(mockWebContents);
      const firstInterval = adSkipper.checkInterval;
      
      adSkipper.startAdDetection();
      
      expect(adSkipper.checkInterval).not.toBe(firstInterval);
    });
  });

  describe('stopAdDetection', () => {
    test('should clear interval and set to null', () => {
      adSkipper.registerWebContents(mockWebContents);
      
      adSkipper.stopAdDetection();
      
      expect(adSkipper.checkInterval).toBe(null);
    });

    test('should handle null interval gracefully', () => {
      expect(() => adSkipper.stopAdDetection()).not.toThrow();
    });
  });

  describe('checkForAds', () => {
    beforeEach(() => {
      adSkipper.registerWebContents(mockWebContents);
    });

    test('should return early if web contents is destroyed', async () => {
      mockWebContents.isDestroyed.mockReturnValue(true);
      
      await adSkipper.checkForAds();
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should return early if currently skipping', async () => {
      adSkipper.isCurrentlySkipping = true;
      
      await adSkipper.checkForAds();
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should execute JavaScript to detect ads', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: false,
        skipButtonAvailable: false
      });
      
      await adSkipper.checkForAds();
      
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should skip ad when ad is playing and skip button is available', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: true,
        skipButtonAvailable: true,
        skipButtonSelector: 'ytp-ad-skip-button'
      });
      
      const skipAdSpy = jest.spyOn(adSkipper, 'skipAd').mockResolvedValue();
      
      await adSkipper.checkForAds();
      
      expect(skipAdSpy).toHaveBeenCalled();
    });

    test('should not skip ad when ad is playing but skip button not available', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue({
        isAdPlaying: true,
        skipButtonAvailable: false
      });
      
      const skipAdSpy = jest.spyOn(adSkipper, 'skipAd').mockResolvedValue();
      
      await adSkipper.checkForAds();
      
      expect(skipAdSpy).not.toHaveBeenCalled();
    });

    test('should handle JavaScript execution errors gracefully', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Test error'));
      
      await expect(adSkipper.checkForAds()).resolves.not.toThrow();
    });

    test('should stop detection if web contents is destroyed', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Object has been destroyed'));
      const stopDetectionSpy = jest.spyOn(adSkipper, 'stopAdDetection');
      
      await adSkipper.checkForAds();
      
      expect(stopDetectionSpy).toHaveBeenCalled();
    });
  });

  describe('skipAd', () => {
    beforeEach(() => {
      adSkipper.registerWebContents(mockWebContents);
    });

    test('should return early if currently skipping', async () => {
      adSkipper.isCurrentlySkipping = true;
      
      await adSkipper.skipAd();
      
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should execute JavaScript to click skip button', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      
      await adSkipper.skipAd();
      
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should increment skip count when successful', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      const initialCount = adSkipper.adSkippedCount;
      
      await adSkipper.skipAd();
      
      expect(adSkipper.adSkippedCount).toBe(initialCount + 1);
    });

    test('should update last skip time when successful', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue(true);
      const beforeTime = Date.now();
      
      await adSkipper.skipAd();
      
      expect(adSkipper.lastAdSkipTime).toBeGreaterThanOrEqual(beforeTime);
    });

    test('should not increment count when skip fails', async () => {
      mockWebContents.executeJavaScript.mockResolvedValue(false);
      const initialCount = adSkipper.adSkippedCount;
      
      await adSkipper.skipAd();
      
      expect(adSkipper.adSkippedCount).toBe(initialCount);
    });

    test('should handle JavaScript execution errors', async () => {
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('Test error'));
      
      await expect(adSkipper.skipAd()).resolves.not.toThrow();
    });

    test('should set isCurrentlySkipping flag during execution', async () => {
      let isSkippingDuringExecution = false;
      mockWebContents.executeJavaScript.mockImplementation(() => {
        isSkippingDuringExecution = adSkipper.isCurrentlySkipping;
        return Promise.resolve(true);
      });
      
      await adSkipper.skipAd();
      
      expect(isSkippingDuringExecution).toBe(true);
    });
  });

  describe('enable/disable', () => {
    test('should enable ad skipping and start detection', () => {
      adSkipper.isEnabled = false;
      adSkipper.registerWebContents(mockWebContents);
      const startDetectionSpy = jest.spyOn(adSkipper, 'startAdDetection');
      
      adSkipper.enable();
      
      expect(adSkipper.isEnabled).toBe(true);
      expect(startDetectionSpy).toHaveBeenCalled();
    });

    test('should disable ad skipping and stop detection', () => {
      adSkipper.registerWebContents(mockWebContents);
      const stopDetectionSpy = jest.spyOn(adSkipper, 'stopAdDetection');
      
      adSkipper.disable();
      
      expect(adSkipper.isEnabled).toBe(false);
      expect(stopDetectionSpy).toHaveBeenCalled();
    });
  });

  describe('getStats', () => {
    test('should return current statistics', () => {
      adSkipper.adSkippedCount = 5;
      adSkipper.lastAdSkipTime = 12345;
      adSkipper.isCurrentlySkipping = true;
      
      const stats = adSkipper.getStats();
      
      expect(stats).toEqual({
        isEnabled: true,
        adSkippedCount: 5,
        lastAdSkipTime: 12345,
        isCurrentlySkipping: true
      });
    });
  });

  describe('resetStats', () => {
    test('should reset statistics to zero', () => {
      adSkipper.adSkippedCount = 10;
      adSkipper.lastAdSkipTime = 54321;
      
      adSkipper.resetStats();
      
      expect(adSkipper.adSkippedCount).toBe(0);
      expect(adSkipper.lastAdSkipTime).toBe(0);
    });
  });
});
