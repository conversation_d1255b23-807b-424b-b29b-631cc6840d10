/**
 * YouTube Ad Skipper
 * Detects and automatically skips YouTube ads when the skip button becomes available
 */

class YouTubeAdSkipper {
  constructor() {
    this.isEnabled = true;
    this.checkInterval = null;
    this.adDetectionInterval = 1000; // Check every 1 second
    this.webContents = null;
    this.adSkippedCount = 0;
    this.lastAdSkipTime = 0;
    this.isCurrentlySkipping = false;
  }

  /**
   * Register web contents for ad skipping
   * @param {Electron.WebContents} webContents - The YouTube web contents
   */
  registerWebContents(webContents) {
    this.webContents = webContents;
    console.log('YouTube Ad Skipper: Web contents registered');
    this.startAdDetection();
  }

  /**
   * Unregister web contents
   */
  unregisterWebContents() {
    this.stopAdDetection();
    this.webContents = null;
    console.log('YouTube Ad Skipper: Web contents unregistered');
  }

  /**
   * Start the ad detection process
   */
  startAdDetection() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    if (!this.isEnabled || !this.webContents) {
      return;
    }

    console.log('YouTube Ad Skipper: Starting ad detection');
    
    this.checkInterval = setInterval(() => {
      this.checkForAds();
    }, this.adDetectionInterval);
  }

  /**
   * Stop the ad detection process
   */
  stopAdDetection() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('YouTube Ad Skipper: Stopped ad detection');
    }
  }

  /**
   * Check for ads and skip them if possible
   */
  async checkForAds() {
    if (!this.webContents || this.webContents.isDestroyed() || this.isCurrentlySkipping) {
      return;
    }

    try {
      const adInfo = await this.webContents.executeJavaScript(`
        (function() {
          // Check for various ad indicators
          const adIndicators = {
            // Skip button selectors (multiple variations)
            skipButtons: [
              '.ytp-ad-skip-button',
              '.ytp-ad-skip-button-modern',
              '.ytp-skip-ad-button',
              'button[class*="skip"]',
              'button[aria-label*="Skip"]',
              'button[aria-label*="skip"]',
              '.ytp-ad-skip-button-container button',
              '[data-testid="skip-button"]'
            ],
            
            // Ad overlay indicators
            adOverlays: [
              '.ytp-ad-overlay-container',
              '.ytp-ad-text',
              '.ytp-ad-duration-remaining',
              '.ytp-ad-preview-container',
              '.ad-showing',
              '[class*="ad-overlay"]'
            ],
            
            // Video ad indicators
            videoAdIndicators: [
              '.video-ads',
              '.ytp-ad-module',
              '.ytp-ad-player-overlay',
              '.ytp-ad-image-overlay'
            ]
          };

          // Check if an ad is currently playing
          let isAdPlaying = false;
          let skipButtonAvailable = false;
          let skipButton = null;
          let adTimeRemaining = null;

          // Check for ad overlays
          for (const selector of adIndicators.adOverlays) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) {
              isAdPlaying = true;
              break;
            }
          }

          // Check for video ad indicators
          if (!isAdPlaying) {
            for (const selector of adIndicators.videoAdIndicators) {
              const element = document.querySelector(selector);
              if (element && element.offsetParent !== null) {
                isAdPlaying = true;
                break;
              }
            }
          }

          // Check for "Ad" text in video player
          if (!isAdPlaying) {
            const adTexts = document.querySelectorAll('*');
            for (const element of adTexts) {
              const text = element.textContent || '';
              if (text.includes('Ad ·') || text.includes('Advertisement') || 
                  text.match(/^Ad \\d+/) || text.includes('Skip Ad')) {
                if (element.offsetParent !== null) {
                  isAdPlaying = true;
                  break;
                }
              }
            }
          }

          // Look for skip button if ad is playing
          if (isAdPlaying) {
            for (const selector of adIndicators.skipButtons) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null && !button.disabled) {
                skipButton = button;
                skipButtonAvailable = true;
                break;
              }
            }

            // Get ad time remaining if available
            const timeElement = document.querySelector('.ytp-ad-duration-remaining');
            if (timeElement) {
              adTimeRemaining = timeElement.textContent;
            }
          }

          return {
            isAdPlaying,
            skipButtonAvailable,
            skipButtonSelector: skipButton ? skipButton.className : null,
            adTimeRemaining,
            timestamp: Date.now()
          };
        })();
      `);

      if (adInfo.isAdPlaying) {
        console.log('YouTube Ad Skipper: Ad detected', {
          skipAvailable: adInfo.skipButtonAvailable,
          timeRemaining: adInfo.adTimeRemaining
        });

        if (adInfo.skipButtonAvailable) {
          await this.skipAd();
        }
      }

    } catch (error) {
      // Silently handle errors to avoid spam in console
      if (error.message.includes('destroyed')) {
        this.stopAdDetection();
      }
    }
  }

  /**
   * Skip the current ad
   */
  async skipAd() {
    if (this.isCurrentlySkipping) {
      return;
    }

    this.isCurrentlySkipping = true;
    const currentTime = Date.now();

    try {
      const skipped = await this.webContents.executeJavaScript(`
        (function() {
          const skipSelectors = [
            '.ytp-ad-skip-button',
            '.ytp-ad-skip-button-modern',
            '.ytp-skip-ad-button',
            'button[class*="skip"]',
            'button[aria-label*="Skip"]',
            'button[aria-label*="skip"]',
            '.ytp-ad-skip-button-container button',
            '[data-testid="skip-button"]'
          ];

          for (const selector of skipSelectors) {
            const button = document.querySelector(selector);
            if (button && button.offsetParent !== null && !button.disabled) {
              console.log('YouTube Ad Skipper: Clicking skip button:', selector);
              button.click();
              return true;
            }
          }

          return false;
        })();
      `);

      if (skipped) {
        this.adSkippedCount++;
        this.lastAdSkipTime = currentTime;
        console.log(`YouTube Ad Skipper: Successfully skipped ad #${this.adSkippedCount}`);
        
        // Brief pause after skipping to avoid rapid clicking
        setTimeout(() => {
          this.isCurrentlySkipping = false;
        }, 2000);
      } else {
        this.isCurrentlySkipping = false;
      }

    } catch (error) {
      console.warn('YouTube Ad Skipper: Failed to skip ad:', error.message);
      this.isCurrentlySkipping = false;
    }
  }

  /**
   * Enable ad skipping
   */
  enable() {
    this.isEnabled = true;
    console.log('YouTube Ad Skipper: Enabled');
    if (this.webContents) {
      this.startAdDetection();
    }
  }

  /**
   * Disable ad skipping
   */
  disable() {
    this.isEnabled = false;
    this.stopAdDetection();
    console.log('YouTube Ad Skipper: Disabled');
  }

  /**
   * Get statistics about ad skipping
   */
  getStats() {
    return {
      isEnabled: this.isEnabled,
      adSkippedCount: this.adSkippedCount,
      lastAdSkipTime: this.lastAdSkipTime,
      isCurrentlySkipping: this.isCurrentlySkipping
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.adSkippedCount = 0;
    this.lastAdSkipTime = 0;
    console.log('YouTube Ad Skipper: Statistics reset');
  }
}

module.exports = YouTubeAdSkipper;
